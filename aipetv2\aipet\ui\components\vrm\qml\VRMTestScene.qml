// VRM测试场景
// 快速验证Qt Quick 3D和VRM控制器功能

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick3D 6.0
import VRM 1.0
import "." as VRMComponents

ApplicationWindow {
    id: testWindow
    
    title: "Qt Quick 3D VRM 快速测试"
    width: 1000
    height: 700
    visible: true
    
    // VRM控制器
    VRMController {
        id: vrmController
        
        Component.onCompleted: {
            console.log("VRM控制器已创建")
            debugLog("QML组件初始化完成")
        }
    }
    
    // 主布局
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // 标题栏
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 50
            color: "#2a2a2a"
            
            RowLayout {
                anchors.fill: parent
                anchors.margins: 10
                
                Text {
                    text: "🎭 Qt Quick 3D VRM 测试环境"
                    color: "white"
                    font.pixelSize: 18
                    font.bold: true
                }
                
                Item { Layout.fillWidth: true }
                
                Text {
                    text: vrmController.testMessage
                    color: vrmController.modelLoaded ? "lightgreen" : "orange"
                    font.pixelSize: 14
                }
            }
        }
        
        // 3D视图区域
        View3D {
            id: view3d
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            environment: SceneEnvironment {
                backgroundMode: SceneEnvironment.Color
                clearColor: "#1a1a1a"
                antialiasingMode: SceneEnvironment.MSAA
                antialiasingQuality: SceneEnvironment.High
            }
            
            // 相机
            PerspectiveCamera {
                id: camera
                position: Qt.vector3d(0, 0, 5)
                eulerRotation.x: 0
                fieldOfView: 45
                
                // 相机动画
                SequentialAnimation on eulerRotation.y {
                    running: vrmController.modelLoaded
                    loops: Animation.Infinite
                    NumberAnimation {
                        from: 0
                        to: 360
                        duration: 10000
                    }
                }
            }
            
            // 主光源
            DirectionalLight {
                id: mainLight
                eulerRotation.x: -30
                eulerRotation.y: -30
                brightness: 1.0
                castsShadow: true
                color: "#ffffff"
            }
            
            // 补光
            DirectionalLight {
                id: fillLight
                eulerRotation.x: 30
                eulerRotation.y: 150
                brightness: 0.3
                castsShadow: false
                color: "#87ceeb"
            }
            
            // 环境光
            DirectionalLight {
                id: ambientLight
                eulerRotation.x: 90
                brightness: 0.1
                color: "#404040"
            }
            
            // VRM几何体显示节点
            Node {
                id: vrmModelNode
                visible: vrmController.modelLoaded
                
                // 旋转动画
                RotationAnimation on eulerRotation.y {
                    running: vrmController.modelLoaded
                    from: 0
                    to: 360
                    duration: 8000
                    loops: Animation.Infinite
                }

                // 浮动动画
                SequentialAnimation on position.y {
                    running: vrmController.modelLoaded
                    loops: Animation.Infinite
                    NumberAnimation {
                        from: 0
                        to: 0.3
                        duration: 3000
                        easing.type: Easing.InOutSine
                    }
                    NumberAnimation {
                        from: 0.3
                        to: 0
                        duration: 3000
                        easing.type: Easing.InOutSine
                    }
                }
                
                // VRM几何体显示 - 模拟人形结构
                Node {
                    id: vrmGeometryDisplay
                    visible: vrmController.modelLoaded
                    
                    // 头部
                    Model {
                        source: "#Sphere"
                        scale: Qt.vector3d(0.4, 0.4, 0.4)
                        position: Qt.vector3d(0, 1.5, 0)
                        materials: PrincipledMaterial {
                            baseColor: "#ffdbac"  // 肤色
                            metalness: 0.0
                            roughness: 0.8
                        }
                    }
                    
                    // 身体
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.6, 1.0, 0.3)
                        position: Qt.vector3d(0, 0.5, 0)
                        materials: PrincipledMaterial {
                            baseColor: "#4a90e2"  // 蓝色衣服
                            metalness: 0.0
                            roughness: 0.7
                        }
                    }
                    
                    // 左臂
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.2, 0.8, 0.2)
                        position: Qt.vector3d(-0.5, 0.5, 0)
                        materials: PrincipledMaterial {
                            baseColor: "#ffdbac"
                            metalness: 0.0
                            roughness: 0.8
                        }
                    }
                    
                    // 右臂
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.2, 0.8, 0.2)
                        position: Qt.vector3d(0.5, 0.5, 0)
                        materials: PrincipledMaterial {
                            baseColor: "#ffdbac"
                            metalness: 0.0
                            roughness: 0.8
                        }
                    }
                    
                    // 左腿
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.25, 1.0, 0.25)
                        position: Qt.vector3d(-0.2, -0.5, 0)
                        materials: PrincipledMaterial {
                            baseColor: "#2c3e50"  // 深色裤子
                            metalness: 0.0
                            roughness: 0.9
                        }
                    }
                    
                    // 右腿
                    Model {
                        source: "#Cube"
                        scale: Qt.vector3d(0.25, 1.0, 0.25)
                        position: Qt.vector3d(0.2, -0.5, 0)
                        materials: PrincipledMaterial {
                            baseColor: "#2c3e50"
                            metalness: 0.0
                            roughness: 0.9
                        }
                    }
                    
                    // 显示几何体数量的小立方体（代表VRM的复杂性）
                    Repeater {
                        model: Math.min(vrmController.geometryCount, 12)  // 最多显示12个
                        
                        delegate: Model {
                            source: "#Cube"
                            scale: Qt.vector3d(0.1, 0.1, 0.1)
                            position: Qt.vector3d(
                                (index % 4 - 1.5) * 0.3,
                                2.5 + Math.floor(index / 4) * 0.2,
                                0
                            )
                            
                            materials: PrincipledMaterial {
                                baseColor: {
                                    var colors = ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57", "#ff9ff3", "#54a0ff", "#5f27cd", "#fd79a8", "#fdcb6e", "#6c5ce7", "#a29bfe"]
                                    return colors[index % colors.length]
                                }
                                metalness: 0.2
                                roughness: 0.3
                            }
                            
                            // 小立方体的旋转动画
                            RotationAnimation on eulerRotation.y {
                                running: vrmController.modelLoaded
                                from: 0
                                to: 360
                                duration: 2000 + (index * 100)
                                loops: Animation.Infinite
                            }
                        }
                    }
                }
                

            }

            // 回退立方体（当没有VRM数据时显示）
            Model {
                id: fallbackCube
                visible: !vrmController.modelLoaded

                source: "#Cube"
                scale: Qt.vector3d(1.0, 1.0, 1.0)

                materials: PrincipledMaterial {
                    baseColor: "#666666"
                    metalness: 0.1
                    roughness: 0.7
                }

                // 缓慢旋转
                RotationAnimation on eulerRotation.y {
                    running: !vrmController.modelLoaded
                    from: 0
                    to: 360
                    duration: 10000
                    loops: Animation.Infinite
                }
            }
            
            // 地面
            Model {
                source: "#Rectangle"
                scale: Qt.vector3d(10, 10, 1)
                eulerRotation.x: -90
                position.y: -2
                
                materials: PrincipledMaterial {
                    baseColor: "#2a2a2a"
                    metalness: 0.0
                    roughness: 0.8
                }
            }
            
            // 状态文本（3D空间中）
            Node {
                position: Qt.vector3d(0, 3, 0)
                visible: !vrmController.modelLoaded  // 只在未加载时显示
                
                Text {
                    anchors.centerIn: parent
                    text: "⏳ 等待加载VRM模型..."
                    color: "orange"
                    font.pixelSize: 14
                    horizontalAlignment: Text.AlignHCenter
                }
            }
        }
        
        // 控制面板
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 120
            color: "#1a1a1a"
            border.color: "#333333"
            border.width: 1
            
            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 15
                spacing: 10
                
                // 按钮行
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 15
                    
                    Button {
                        text: "加载 Zome.vrm"
                        onClicked: {
                            console.log("点击加载Zome.vrm")
                            vrmController.loadModel("aipet/assets/vrm/models/Zome.vrm")
                        }
                        
                        flat: true
                        
                        background: Rectangle {
                            color: parent.pressed ? "#4a90e2" : "#3a7bd5"
                            radius: 5
                        }
                    }
                    
                    Button {
                        text: "加载 Alice.vrm"
                        onClicked: {
                            console.log("点击加载Alice.vrm")
                            vrmController.loadModel("aipet/assets/vrm/models/Alice.vrm")
                        }
                        
                        flat: true
                        
                        background: Rectangle {
                            color: parent.pressed ? "#e74c3c" : "#c0392b"
                            radius: 5
                        }
                    }
                    
                    Button {
                        text: "清除模型"
                        onClicked: {
                            console.log("点击清除模型")
                            vrmController.clearModel()
                        }
                        
                        flat: true
                        
                        background: Rectangle {
                            color: parent.pressed ? "#95a5a6" : "#7f8c8d"
                            radius: 5
                        }
                    }
                    
                    Button {
                        text: "测试连接"
                        onClicked: {
                            console.log("点击测试连接")
                            vrmController.testConnection()
                        }
                        
                        flat: true
                        
                        background: Rectangle {
                            color: parent.pressed ? "#f39c12" : "#e67e22"
                            radius: 5
                        }
                    }
                    
                    Item { Layout.fillWidth: true }
                }
                
                // 状态信息行
                RowLayout {
                    Layout.fillWidth: true
                    spacing: 20
                    
                    Text {
                        text: vrmController.modelLoaded ? 
                              "状态: ✅ 已加载 " + vrmController.modelName + " (几何体:" + vrmController.geometryCount + " 材质:" + vrmController.materialCount + ")" :
                              "状态: ⏳ 未加载"
                        color: vrmController.modelLoaded ? "lightgreen" : "orange"
                        font.pixelSize: 14
                    }
                    
                    Text {
                        text: "Qt Quick 3D: ✅ 可用"
                        color: "lightgreen"
                        font.pixelSize: 14
                    }
                    
                    Text {
                        text: "Python-QML: ✅ 连接正常"
                        color: "lightgreen"
                        font.pixelSize: 14
                    }
                    
                    Item { Layout.fillWidth: true }
                    
                    Text {
                        text: "FPS: 60"
                        color: "lightblue"
                        font.pixelSize: 14
                    }
                }
            }
        }
    }
    
    // 键盘快捷键
    Item {
        focus: true
        Keys.onPressed: {
            switch(event.key) {
                case Qt.Key_1:
                    vrmController.loadModel("aipet/assets/vrm/models/Zome.vrm")
                    break
                case Qt.Key_2:
                    vrmController.loadModel("aipet/assets/vrm/models/Alice.vrm")
                    break
                case Qt.Key_C:
                    vrmController.clearModel()
                    break
                case Qt.Key_T:
                    vrmController.testConnection()
                    break
            }
        }
    }
    
    // 监听模型变化
    Connections {
        target: vrmController
        function onModelChanged() {
            console.log("🔄 模型数据已更新:")
            console.log("  - 模型已加载:", vrmController.modelLoaded)
            console.log("  - 模型名称:", vrmController.modelName)
            console.log("  - 几何体数量:", vrmController.geometryCount)
            console.log("  - 材质数量:", vrmController.materialCount)
        }
    }
    
    // 组件完成时的初始化
    Component.onCompleted: {
        console.log("VRM测试场景初始化完成")
        vrmController.debugLog("测试场景已就绪")
        
        // 输出初始状态
        console.log("初始状态:")
        console.log("  - 模型已加载:", vrmController.modelLoaded)
        console.log("  - 几何体数量:", vrmController.geometryCount)
    }
}

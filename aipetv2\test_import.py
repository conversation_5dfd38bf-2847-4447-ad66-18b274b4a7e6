#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入VRM组件
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🔄 测试导入...")

try:
    print("导入PySide6...")
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QObject
    print("✅ PySide6导入成功")
    
    print("导入VRMDataProvider...")
    from aipet.ui.components.vrm.vrm_data_provider import VRMDataProvider
    print("✅ VRMDataProvider导入成功")
    
    print("导入VRMController...")
    from aipet.ui.components.vrm.vrm_controller import VRMController
    print("✅ VRMController导入成功")
    
    print("导入pygltflib...")
    import pygltflib
    print("✅ pygltflib导入成功")
    
    print("🎉 所有导入成功!")
    
except Exception as e:
    print(f"❌ 导入失败: {str(e)}")
    import traceback
    traceback.print_exc()

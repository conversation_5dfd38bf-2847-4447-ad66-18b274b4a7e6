#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VRM控制器

作为Python和QML之间的桥接，提供VRM模型控制接口
管理VRMDataProvider实例，处理用户交互
"""

from PySide6.QtCore import QObject, Signal, Property, Slot
from .vrm_data_provider import VRMDataProvider
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class VRMController(QObject):
    """VRM控制器 - Python和QML之间的桥梁"""
    
    # 信号定义
    modelChanged = Signal()
    expressionChanged = Signal(str, float)  # 表情名称, 权重
    animationStarted = Signal(str)          # 动画名称
    animationFinished = Signal(str)         # 动画名称
    animationProgress = Signal(str, float)  # 动画名称, 进度(0.0-1.0)
    boneTransformChanged = Signal(str, "QVariantMap")  # 骨骼名称, 变换数据
    errorOccurred = Signal(str)             # 错误信息
    loadingProgress = Signal(int)           # 加载进度
    metaDataLoaded = Signal("QVariantMap")  # VRM元数据加载完成
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 创建数据提供者
        self._data_provider = VRMDataProvider(self)
        
        # 连接信号
        self._setup_connections()
        
        # 当前状态
        self._current_expressions = {}  # 当前表情权重
        self._animation_playing = False
        self._current_animation = ""
        
        self.logger.info("VRMController 初始化完成")
    
    def _setup_connections(self):
        """设置信号连接"""
        # 连接数据提供者的信号
        self._data_provider.dataChanged.connect(self.modelChanged)
        self._data_provider.errorOccurred.connect(self.errorOccurred)
        self._data_provider.loadingProgress.connect(self.loadingProgress)
        self._data_provider.metaDataChanged.connect(self._on_meta_data_changed)
        self._data_provider.boneDataChanged.connect(self.modelChanged)
        self._data_provider.animationDataChanged.connect(self.modelChanged)
        self._data_provider.expressionDataChanged.connect(self.modelChanged)

        self.logger.debug("信号连接设置完成")

    def _on_meta_data_changed(self):
        """VRM元数据变化处理"""
        meta_data = self._data_provider.vrmMeta
        self.metaDataLoaded.emit(meta_data)
        self.logger.debug(f"VRM元数据已加载: {meta_data.get('name', 'Unknown')}")
    
    # 模型相关属性
    @Property(bool, notify=modelChanged)
    def modelLoaded(self) -> bool:
        """模型是否已加载"""
        return self._data_provider.modelLoaded
    
    @Property(str, notify=modelChanged)
    def modelName(self) -> str:
        """模型名称"""
        return self._data_provider.modelName
    
    @Property(str, notify=modelChanged)
    def modelPath(self) -> str:
        """模型路径"""
        return self._data_provider.modelPath
    
    @Property("QVariantList", notify=modelChanged)
    def geometries(self) -> List[Dict]:
        """几何数据"""
        geometries = self._data_provider.geometries
        self.logger.debug(f"返回几何数据: {len(geometries)} 个几何体")
        return geometries
    
    @Property("QVariantList", notify=modelChanged)
    def materials(self) -> List[Dict]:
        """材质数据"""
        return self._data_provider.materials
    
    @Property("QVariantList", notify=modelChanged)
    def availableExpressions(self) -> List[Dict]:
        """可用表情列表"""
        expressions = []
        for expr in self._data_provider.expressions:
            expressions.append({
                "name": expr["name"],
                "weight": self._current_expressions.get(expr["name"], expr.get("weight", 0.0))
            })
        return expressions
    
    @Property(int, notify=modelChanged)
    def geometryCount(self) -> int:
        """几何体数量"""
        return self._data_provider.geometryCount
    
    @Property(int, notify=modelChanged)
    def materialCount(self) -> int:
        """材质数量"""
        return self._data_provider.materialCount
    
    @Property(int, notify=modelChanged)
    def expressionCount(self) -> int:
        """表情数量"""
        return self._data_provider.expressionCount

    @Property("QVariantList", notify=modelChanged)
    def bones(self) -> List[Dict]:
        """骨骼数据"""
        return self._data_provider.bones

    @Property("QVariantList", notify=modelChanged)
    def animations(self) -> List[Dict]:
        """动画数据"""
        return self._data_provider.animations

    @Property("QVariantMap", notify=metaDataLoaded)
    def vrmMeta(self) -> Dict[str, Any]:
        """VRM元数据"""
        return self._data_provider.vrmMeta

    @Property(int, notify=modelChanged)
    def boneCount(self) -> int:
        """骨骼数量"""
        return self._data_provider.boneCount

    @Property(int, notify=modelChanged)
    def animationCount(self) -> int:
        """动画数量"""
        return self._data_provider.animationCount
    
    @Property(str, notify=modelChanged)
    def testMessage(self) -> str:
        """测试消息"""
        if self._data_provider.modelLoaded:
            return f"✅ 已加载: {self._data_provider.modelName} ({len(self._data_provider.geometries)}个几何体)"
        return "⏳ 等待加载模型..."
    
    # 动画相关属性
    @Property(bool, notify=animationStarted)
    def animationPlaying(self) -> bool:
        """动画是否正在播放"""
        return self._animation_playing
    
    @Property(str, notify=animationStarted)
    def currentAnimation(self) -> str:
        """当前播放的动画"""
        return self._current_animation
    
    # 模型管理方法
    @Slot(str, result=bool)
    def loadModel(self, file_path: str) -> bool:
        """
        加载VRM模型
        
        Args:
            file_path: VRM文件路径
            
        Returns:
            bool: 加载是否成功
        """
        self.logger.info(f"请求加载VRM模型: {file_path}")
        
        try:
            # 清除当前表情状态
            self._current_expressions.clear()
            
            # 加载模型
            success = self._data_provider.load_model(file_path)
            
            if success:
                # 初始化表情权重
                for expr in self._data_provider.expressions:
                    self._current_expressions[expr["name"]] = expr.get("weight", 0.0)
                
                self.logger.info(f"VRM模型加载成功: {self._data_provider.modelName}")
            else:
                self.logger.error(f"VRM模型加载失败: {file_path}")
            
            return success
            
        except Exception as e:
            error_msg = f"加载VRM模型时发生异常: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self.errorOccurred.emit(error_msg)
            return False
    
    @Slot()
    def clearModel(self):
        """清除当前模型"""
        self.logger.info("清除VRM模型")
        
        # 停止动画
        if self._animation_playing:
            self.stopAnimation()
        
        # 清除数据
        self._data_provider.clear_model()
        self._current_expressions.clear()
        
        self.modelChanged.emit()
    
    @Slot(result="QVariantMap")
    def getModelInfo(self) -> Dict[str, Any]:
        """获取模型详细信息"""
        info = self._data_provider.get_model_info()
        info.update({
            "animationPlaying": self._animation_playing,
            "currentAnimation": self._current_animation,
            "expressionWeights": self._current_expressions.copy()
        })
        return info
    
    # 表情控制方法
    @Slot(str, float)
    def setExpression(self, name: str, weight: float):
        """
        设置表情权重
        
        Args:
            name: 表情名称
            weight: 权重值 (0.0-1.0)
        """
        # 限制权重范围
        weight = max(0.0, min(1.0, weight))
        
        # 检查表情是否存在
        expression_names = [expr["name"] for expr in self._data_provider.expressions]
        if name not in expression_names:
            self.logger.warning(f"表情不存在: {name}")
            return
        
        # 更新权重
        old_weight = self._current_expressions.get(name, 0.0)
        self._current_expressions[name] = weight
        
        self.logger.debug(f"设置表情 {name}: {old_weight:.2f} -> {weight:.2f}")
        
        # 发送信号
        self.expressionChanged.emit(name, weight)
        self.modelChanged.emit()  # 触发availableExpressions更新
    
    @Slot(str, result=float)
    def getExpressionWeight(self, name: str) -> float:
        """获取表情权重"""
        return self._current_expressions.get(name, 0.0)
    
    @Slot()
    def resetExpressions(self):
        """重置所有表情到默认状态"""
        self.logger.info("重置所有表情")
        
        for expr in self._data_provider.expressions:
            name = expr["name"]
            default_weight = expr.get("weight", 0.0)
            if name in self._current_expressions:
                self._current_expressions[name] = default_weight
                self.expressionChanged.emit(name, default_weight)
        
        self.modelChanged.emit()
    
    @Slot(str, float, int)
    def animateExpression(self, name: str, target_weight: float, duration_ms: int):
        """
        动画过渡到目标表情
        
        Args:
            name: 表情名称
            target_weight: 目标权重
            duration_ms: 动画时长(毫秒)
        """
        self.logger.info(f"表情动画: {name} -> {target_weight:.2f} ({duration_ms}ms)")
        
        # TODO: 实现表情动画逻辑
        # 目前直接设置目标值
        self.setExpression(name, target_weight)
    
    # 动画控制方法
    @Slot(str)
    def playAnimation(self, animation_name: str):
        """
        播放动画
        
        Args:
            animation_name: 动画名称
        """
        self.logger.info(f"播放动画: {animation_name}")
        
        # 停止当前动画
        if self._animation_playing:
            self.stopAnimation()
        
        # 开始新动画
        self._animation_playing = True
        self._current_animation = animation_name
        
        self.animationStarted.emit(animation_name)
        
        # TODO: 实现实际的动画播放逻辑
    
    @Slot()
    def stopAnimation(self):
        """停止当前动画"""
        if self._animation_playing:
            self.logger.info(f"停止动画: {self._current_animation}")
            
            old_animation = self._current_animation
            self._animation_playing = False
            self._current_animation = ""
            
            self.animationFinished.emit(old_animation)
    
    @Slot(result="QStringList")
    def getAvailableAnimations(self) -> List[str]:
        """获取可用动画列表"""
        animations = []
        for anim in self._data_provider.animations:
            animations.append(anim.get("name", "Unknown"))

        # 如果没有动画数据，返回默认动画
        if not animations:
            animations = ["idle", "wave", "nod"]

        return animations
    
    # 调试和测试方法
    @Slot(result=bool)
    def testConnection(self) -> bool:
        """测试Python-QML连接"""
        self.logger.info("测试Python-QML连接")
        return True
    
    @Slot(str)
    def debugLog(self, message: str):
        """从QML发送调试日志"""
        self.logger.debug(f"QML调试: {message}")
    
    @Slot(result="QVariantMap")
    def getDebugInfo(self) -> Dict[str, Any]:
        """获取调试信息"""
        return {
            "controllerReady": True,
            "dataProviderReady": self._data_provider is not None,
            "modelLoaded": self.modelLoaded,
            "expressionCount": len(self._current_expressions),
            "animationPlaying": self._animation_playing,
            "boneCount": self.boneCount,
            "animationCount": self.animationCount,
            "vrmVersion": self._detect_vrm_version()
        }

    def _detect_vrm_version(self) -> str:
        """检测VRM版本"""
        meta = self.vrmMeta
        if not meta:
            return "Unknown"

        # 根据元数据字段判断版本
        if 'authors' in meta:
            return "VRM 1.0"
        elif 'author' in meta:
            return "VRM 0.x"
        else:
            return "Unknown"

    # 骨骼控制方法
    @Slot(str, result="QVariantMap")
    def getBoneTransform(self, bone_name: str) -> Dict[str, Any]:
        """获取骨骼变换信息"""
        for bone in self._data_provider.bones:
            if bone.get("name") == bone_name:
                return {
                    "translation": bone.get("translation", [0.0, 0.0, 0.0]),
                    "rotation": bone.get("rotation", [0.0, 0.0, 0.0, 1.0]),
                    "scale": bone.get("scale", [1.0, 1.0, 1.0])
                }

        self.logger.warning(f"骨骼不存在: {bone_name}")
        return {}

    @Slot(str, "QVariantList")
    def setBoneTranslation(self, bone_name: str, translation: List[float]):
        """设置骨骼位移"""
        if len(translation) != 3:
            self.logger.error("位移参数必须是3个浮点数")
            return

        # 查找并更新骨骼数据
        for bone in self._data_provider.bones:
            if bone.get("name") == bone_name:
                bone["translation"] = list(translation)
                self.boneTransformChanged.emit(bone_name, {
                    "translation": translation,
                    "rotation": bone.get("rotation", [0.0, 0.0, 0.0, 1.0]),
                    "scale": bone.get("scale", [1.0, 1.0, 1.0])
                })
                self.logger.debug(f"设置骨骼 {bone_name} 位移: {translation}")
                return

        self.logger.warning(f"骨骼不存在: {bone_name}")

    @Slot(str, "QVariantList")
    def setBoneRotation(self, bone_name: str, rotation: List[float]):
        """设置骨骼旋转(四元数)"""
        if len(rotation) != 4:
            self.logger.error("旋转参数必须是4个浮点数(四元数)")
            return

        # 查找并更新骨骼数据
        for bone in self._data_provider.bones:
            if bone.get("name") == bone_name:
                bone["rotation"] = list(rotation)
                self.boneTransformChanged.emit(bone_name, {
                    "translation": bone.get("translation", [0.0, 0.0, 0.0]),
                    "rotation": rotation,
                    "scale": bone.get("scale", [1.0, 1.0, 1.0])
                })
                self.logger.debug(f"设置骨骼 {bone_name} 旋转: {rotation}")
                return

        self.logger.warning(f"骨骼不存在: {bone_name}")

    # 高级表情控制方法
    @Slot("QVariantMap")
    def setMultipleExpressions(self, expression_weights: Dict[str, float]):
        """同时设置多个表情权重"""
        self.logger.debug(f"设置多个表情: {expression_weights}")

        for name, weight in expression_weights.items():
            if isinstance(weight, (int, float)):
                self.setExpression(name, float(weight))

    @Slot(str, result="QVariantList")
    def getExpressionMorphTargets(self, expression_name: str) -> List[Dict]:
        """获取表情的变形目标信息"""
        for expr in self._data_provider.expressions:
            if expr.get("name") == expression_name:
                return expr.get("morphTargets", [])

        self.logger.warning(f"表情不存在: {expression_name}")
        return []

    @Slot(result="QVariantMap")
    def getAllExpressionWeights(self) -> Dict[str, float]:
        """获取所有表情的当前权重"""
        return self._current_expressions.copy()

    # 错误处理和验证方法
    @Slot(result=bool)
    def validateModel(self) -> bool:
        """验证当前模型的完整性"""
        if not self.modelLoaded:
            self.errorOccurred.emit("没有加载模型")
            return False

        # 检查基本数据
        if self.geometryCount == 0:
            self.errorOccurred.emit("模型没有几何数据")
            return False

        if self.materialCount == 0:
            self.logger.warning("模型没有材质数据")

        self.logger.info("模型验证通过")
        return True

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的VRM解析测试
不使用Qt应用程序，直接测试解析功能
"""

import sys
import os
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_vrm_parsing():
    """测试VRM解析功能"""
    
    # 检查依赖
    try:
        import pygltflib
        logger.info("✅ pygltflib 库已安装")
    except ImportError:
        logger.error("❌ pygltflib 库未安装，请运行: pip install pygltflib")
        return False
    
    # 测试文件路径
    test_files = [
        "aipetv2/aipet/assets/vrm/models/Zome.vrm",
        "aipetv2/aipet/assets/vrm/models/Alice.vrm", 
        "aipetv2/aipet/assets/vrm/models/aldina.vrm"
    ]
    
    logger.info("🔄 开始测试VRM文件解析...")
    
    for model_path in test_files:
        full_path = project_root / model_path
        if full_path.exists():
            logger.info(f"\n📁 测试模型: {model_path}")
            logger.info(f"文件大小: {full_path.stat().st_size / (1024*1024):.1f} MB")
            
            try:
                # 直接使用pygltflib解析
                gltf_data = pygltflib.GLTF2.load_binary(str(full_path))
                logger.info("✅ GLTF数据加载成功")
                
                # 检查基本结构
                logger.info(f"📊 节点数量: {len(gltf_data.nodes) if gltf_data.nodes else 0}")
                logger.info(f"📊 网格数量: {len(gltf_data.meshes) if gltf_data.meshes else 0}")
                logger.info(f"📊 材质数量: {len(gltf_data.materials) if gltf_data.materials else 0}")
                logger.info(f"📊 动画数量: {len(gltf_data.animations) if gltf_data.animations else 0}")
                
                # 检查VRM扩展
                vrm_extension = None
                if hasattr(gltf_data, 'extensions') and gltf_data.extensions:
                    if 'VRM' in gltf_data.extensions:
                        vrm_extension = gltf_data.extensions['VRM']
                        logger.info("✅ 检测到VRM 0.x扩展")
                    elif 'VRMC_vrm' in gltf_data.extensions:
                        vrm_extension = gltf_data.extensions['VRMC_vrm']
                        logger.info("✅ 检测到VRM 1.0扩展")
                    else:
                        logger.warning("⚠️ 未检测到VRM扩展")
                
                # 检查VRM元数据
                if vrm_extension and 'meta' in vrm_extension:
                    meta = vrm_extension['meta']
                    logger.info("📊 VRM元数据:")
                    logger.info(f"   - 名称: {meta.get('name', meta.get('title', 'Unknown'))}")
                    logger.info(f"   - 版本: {meta.get('version', 'Unknown')}")
                    logger.info(f"   - 作者: {meta.get('authors', meta.get('author', 'Unknown'))}")
                
                # 检查表情数据
                expressions_found = 0
                if vrm_extension:
                    if 'expressions' in vrm_extension:
                        # VRM 1.0
                        expressions_data = vrm_extension['expressions']
                        if 'preset' in expressions_data:
                            expressions_found += len(expressions_data['preset'])
                        if 'custom' in expressions_data:
                            expressions_found += len(expressions_data['custom'])
                        logger.info(f"📊 VRM 1.0 表情数量: {expressions_found}")
                    elif 'blendShapeMaster' in vrm_extension:
                        # VRM 0.x
                        blend_shape_groups = vrm_extension['blendShapeMaster'].get('blendShapeGroups', [])
                        expressions_found = len(blend_shape_groups)
                        logger.info(f"📊 VRM 0.x BlendShape数量: {expressions_found}")
                
                logger.info("✅ 模型解析完成\n")
                
            except Exception as e:
                logger.error(f"❌ 解析失败: {str(e)}")
                
        else:
            logger.warning(f"⚠️ 文件不存在: {full_path}")
    
    return True

def main():
    """主函数"""
    print("🚀 启动简单VRM解析测试")
    
    try:
        success = test_vrm_parsing()
        if success:
            logger.info("🎉 测试完成")
            return 0
        else:
            logger.error("❌ 测试失败")
            return 1
            
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        return 0
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())

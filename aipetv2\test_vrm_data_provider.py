#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的VRMDataProvider
验证VRM文件解析功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, QTimer
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入VRM组件
from aipet.ui.components.vrm.vrm_data_provider import VRMDataProvider

class VRMTestRunner(QObject):
    """VRM测试运行器"""
    
    def __init__(self):
        super().__init__()
        self.app = None
        self.provider = None
        
    def setup_application(self):
        """设置应用程序"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("VRM Data Provider Test")
        
        # 创建VRM数据提供者
        self.provider = VRMDataProvider()
        
        # 连接信号
        self.provider.dataChanged.connect(self.on_data_changed)
        self.provider.loadingProgress.connect(self.on_loading_progress)
        self.provider.errorOccurred.connect(self.on_error_occurred)
        
        logger.info("✅ 应用程序设置完成")
        
    def test_vrm_models(self):
        """测试VRM模型加载"""
        # 测试文件路径
        test_files = [
            "aipetv2/aipet/assets/vrm/models/Zome.vrm",
            "aipetv2/aipet/assets/vrm/models/Alice.vrm",
            "aipetv2/aipet/assets/vrm/models/aldina.vrm"
        ]
        
        logger.info("\n🔄 开始测试VRM模型加载...")
        
        for model_path in test_files:
            full_path = project_root / model_path
            if full_path.exists():
                logger.info(f"\n📁 测试模型: {model_path}")
                logger.info(f"文件大小: {full_path.stat().st_size / (1024*1024):.1f} MB")
                
                # 加载模型
                success = self.provider.load_model(str(full_path))
                
                if success:
                    self.print_model_info()
                else:
                    logger.error(f"❌ 模型加载失败: {model_path}")
                
                # 清除模型准备下一个测试
                self.provider.clear_model()
                logger.info("🧹 模型数据已清除\n")
                
            else:
                logger.warning(f"⚠️ 文件不存在: {full_path}")
    
    def print_model_info(self):
        """打印模型信息"""
        logger.info(f"✅ 模型加载成功!")
        logger.info(f"📊 模型名称: {self.provider.modelName}")
        logger.info(f"📊 几何体数量: {self.provider.geometryCount}")
        logger.info(f"📊 材质数量: {self.provider.materialCount}")
        logger.info(f"📊 表情数量: {self.provider.expressionCount}")
        logger.info(f"📊 骨骼数量: {self.provider.boneCount}")
        logger.info(f"📊 动画数量: {self.provider.animationCount}")
        
        # 打印VRM元数据
        meta = self.provider.vrmMeta
        if meta:
            logger.info(f"📊 VRM元数据:")
            logger.info(f"   - 名称: {meta.get('name', meta.get('title', 'Unknown'))}")
            logger.info(f"   - 版本: {meta.get('version', 'Unknown')}")
            logger.info(f"   - 作者: {meta.get('authors', meta.get('author', 'Unknown'))}")
        
        # 打印表情信息
        if self.provider.expressions:
            logger.info(f"📊 表情列表:")
            for expr in self.provider.expressions[:5]:  # 只显示前5个
                logger.info(f"   - {expr['name']} (类型: {expr.get('type', 'unknown')}, 目标数: {len(expr.get('morphTargets', []))})")
        
        # 打印骨骼信息
        if self.provider.bones:
            logger.info(f"📊 骨骼列表 (前5个):")
            for bone in self.provider.bones[:5]:
                logger.info(f"   - {bone['name']} (索引: {bone['index']}, 子节点: {len(bone.get('children', []))})")
    
    def on_data_changed(self):
        """数据变化回调"""
        logger.debug("📡 数据已更新")
    
    def on_loading_progress(self, progress):
        """加载进度回调"""
        logger.info(f"⏳ 加载进度: {progress}%")
    
    def on_error_occurred(self, error_msg):
        """错误发生回调"""
        logger.error(f"❌ 错误: {error_msg}")
    
    def run_test(self):
        """运行测试"""
        try:
            self.setup_application()
            
            # 使用定时器延迟执行测试，确保Qt事件循环启动
            QTimer.singleShot(100, self.test_vrm_models)
            QTimer.singleShot(5000, self.app.quit)  # 5秒后退出
            
            # 启动事件循环
            return self.app.exec()
            
        except Exception as e:
            logger.error(f"测试运行失败: {str(e)}", exc_info=True)
            return 1

def main():
    """主函数"""
    print("🚀 启动VRM数据提供者测试")
    
    try:
        # 检查依赖
        try:
            import pygltflib
            logger.info("✅ pygltflib 库已安装")
        except ImportError:
            logger.error("❌ pygltflib 库未安装，请运行: pip install pygltflib")
            return 1
        
        # 运行测试
        test_runner = VRMTestRunner()
        return test_runner.run_test()
        
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        return 0
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VRM数据提供者

负责解析VRM文件，提取几何数据、材质信息和表情数据
为Qt Quick 3D渲染提供数据支持
"""

from PySide6.QtCore import QObject, Signal, Property, Slot
from pathlib import Path
import logging
from typing import List, Dict, Optional, Any

logger = logging.getLogger(__name__)

class VRMDataProvider(QObject):
    """VRM数据解析和提供者"""
    
    # 信号定义
    dataChanged = Signal()
    geometryDataChanged = Signal()
    materialDataChanged = Signal()
    expressionDataChanged = Signal()
    boneDataChanged = Signal()
    animationDataChanged = Signal()
    metaDataChanged = Signal()
    loadingProgress = Signal(int)  # 加载进度 0-100
    errorOccurred = Signal(str)    # 错误信息
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 项目根目录
        self._project_root = Path(__file__).parent.parent.parent.parent.parent # aipetv2
        self._real_asset_root = self._project_root.parent / "aipet" # aipet
        
        # 数据存储
        self._model_loaded = False
        self._model_name = ""
        self._model_path = ""
        self._geometries = []
        self._materials = []
        self._expressions = []
        self._textures = {}
        self._bones = []
        self._animations = []
        self._vrm_meta = {}
        
        # VRM解析器（延迟初始化）
        self._gltf_data = None
        self._vrm_extension = None
        
        self.logger.info(f"VRMDataProvider 初始化完成, 项目根目录: {self._project_root}")
    
    # 属性定义
    @Property(bool, notify=dataChanged)
    def modelLoaded(self) -> bool:
        """模型是否已加载"""
        return self._model_loaded
    
    @Property(str, notify=dataChanged)
    def modelName(self) -> str:
        """模型名称"""
        return self._model_name
    
    @Property(str, notify=dataChanged)
    def modelPath(self) -> str:
        """模型文件路径"""
        return self._model_path
    
    @Property("QVariantList", notify=geometryDataChanged)
    def geometries(self) -> List[Dict]:
        """几何数据列表"""
        return self._geometries
    
    @Property("QVariantList", notify=materialDataChanged)
    def materials(self) -> List[Dict]:
        """材质数据列表"""
        return self._materials
    
    @Property("QVariantList", notify=expressionDataChanged)
    def expressions(self) -> List[Dict]:
        """表情数据列表"""
        return self._expressions
    
    @Property(int, notify=dataChanged)
    def geometryCount(self) -> int:
        """几何体数量"""
        return len(self._geometries)
    
    @Property(int, notify=dataChanged)
    def materialCount(self) -> int:
        """材质数量"""
        return len(self._materials)
    
    @Property(int, notify=dataChanged)
    def expressionCount(self) -> int:
        """表情数量"""
        return len(self._expressions)

    @Property("QVariantList", notify=boneDataChanged)
    def bones(self) -> List[Dict]:
        """骨骼数据列表"""
        return self._bones

    @Property("QVariantList", notify=animationDataChanged)
    def animations(self) -> List[Dict]:
        """动画数据列表"""
        return self._animations

    @Property("QVariantMap", notify=metaDataChanged)
    def vrmMeta(self) -> Dict[str, Any]:
        """VRM元数据"""
        return self._vrm_meta

    @Property(int, notify=dataChanged)
    def boneCount(self) -> int:
        """骨骼数量"""
        return len(self._bones)

    @Property(int, notify=dataChanged)
    def animationCount(self) -> int:
        """动画数量"""
        return len(self._animations)

    # 公共方法
    @Slot(str, result=bool)
    def load_vrm_file(self, file_path: str) -> bool:
        """
        加载VRM文件
        
        Args:
            file_path: VRM文件路径
            
        Returns:
            bool: 加载是否成功
        """
        try:
            self.logger.info(f"开始加载VRM文件: {file_path}")
            self.loadingProgress.emit(0)
            
            # 检查文件是否存在，并处理相对路径
            path = Path(file_path)
            if not path.is_absolute():
                # 尝试基于 aipetv2 项目根目录解析
                resolved_path = self._project_root / file_path
                self.logger.debug(f"尝试解析路径: {resolved_path}")

                if not resolved_path.exists():
                    # 如果在 aipetv2 中找不到，尝试基于 aipet 目录解析
                    resolved_path = self._real_asset_root / file_path
                    self.logger.debug(f"在 aipetv2 中未找到，尝试解析路径: {resolved_path}")
                
                path = resolved_path

            if not path.exists():
                error_msg = f"VRM文件不存在: {path}"
                self.logger.error(error_msg)
                self.errorOccurred.emit(error_msg)
                return False
            
            # 检查文件扩展名
            if path.suffix.lower() != '.vrm':
                error_msg = f"不是有效的VRM文件: {file_path}"
                self.logger.error(error_msg)
                self.errorOccurred.emit(error_msg)
                return False
            
            self.loadingProgress.emit(20)
            
            # 解析VRM文件
            if not self._parse_vrm_file(str(path)):
                return False
            
            self.loadingProgress.emit(60)
            
            # 提取数据
            if not self._extract_data():
                return False
            
            self.loadingProgress.emit(100)
            
            # 更新状态
            self._model_loaded = True
            self._model_name = path.stem
            self._model_path = str(path)
            
            # 发送信号
            self.dataChanged.emit()
            self.geometryDataChanged.emit()
            self.materialDataChanged.emit()
            self.expressionDataChanged.emit()
            self.boneDataChanged.emit()
            self.animationDataChanged.emit()
            self.metaDataChanged.emit()

            self.logger.info(f"VRM文件加载成功: {self._model_name}")
            self.logger.info(f"几何体: {len(self._geometries)}, 材质: {len(self._materials)}, 表情: {len(self._expressions)}")
            self.logger.info(f"骨骼: {len(self._bones)}, 动画: {len(self._animations)}")
            
            return True
            
        except Exception as e:
            error_msg = f"加载VRM文件失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self.errorOccurred.emit(error_msg)
            return False
    
    @Slot()
    def clear_model(self):
        """清除当前模型数据"""
        self.logger.info("清除VRM模型数据")
        
        self._model_loaded = False
        self._model_name = ""
        self._model_path = ""
        self._geometries.clear()
        self._materials.clear()
        self._expressions.clear()
        self._textures.clear()
        self._bones.clear()
        self._animations.clear()
        self._vrm_meta.clear()

        self._gltf_data = None
        self._vrm_extension = None

        # 发送信号
        self.dataChanged.emit()
        self.geometryDataChanged.emit()
        self.materialDataChanged.emit()
        self.expressionDataChanged.emit()
        self.boneDataChanged.emit()
        self.animationDataChanged.emit()
        self.metaDataChanged.emit()
    
    @Slot(result="QVariantMap")
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "loaded": self._model_loaded,
            "name": self._model_name,
            "path": self._model_path,
            "geometryCount": len(self._geometries),
            "materialCount": len(self._materials),
            "expressionCount": len(self._expressions)
        }
    
    # 私有方法
    def _parse_vrm_file(self, file_path: str) -> bool:
        """解析VRM文件"""
        try:
            # 导入pygltflib
            import pygltflib
            
            self.logger.debug("使用pygltflib解析VRM文件")
            
            # 加载GLTF数据，指定二进制模式避免编码问题
            self._gltf_data = pygltflib.GLTF2.load_binary(file_path)
            
            # 检查VRM扩展
            if hasattr(self._gltf_data, 'extensions') and self._gltf_data.extensions:
                if 'VRM' in self._gltf_data.extensions:
                    self._vrm_extension = self._gltf_data.extensions['VRM']
                    self.logger.debug("检测到VRM 0.x扩展")
                elif 'VRMC_vrm' in self._gltf_data.extensions:
                    self._vrm_extension = self._gltf_data.extensions['VRMC_vrm']
                    self.logger.debug("检测到VRM 1.0扩展")
                else:
                    self.logger.warning("未检测到VRM扩展，将作为普通GLTF处理")
            
            return True
            
        except ImportError:
            error_msg = "pygltflib库未安装，请运行: pip install pygltflib"
            self.logger.error(error_msg)
            self.errorOccurred.emit(error_msg)
            return False
        except Exception as e:
            error_msg = f"解析VRM文件失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self.errorOccurred.emit(error_msg)
            return False
    
    def _extract_data(self) -> bool:
        """提取VRM数据"""
        try:
            self.logger.debug("开始提取VRM数据")
            
            # 提取几何数据
            self._extract_geometry_data()
            
            # 提取材质数据
            self._extract_material_data()
            
            # 提取表情数据
            self._extract_expression_data()

            # 提取骨骼数据
            self._extract_bone_data()

            # 提取动画数据
            self._extract_animation_data()

            # 提取VRM元数据
            self._extract_vrm_meta()

            return True
            
        except Exception as e:
            error_msg = f"提取VRM数据失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            self.errorOccurred.emit(error_msg)
            return False
    
    def _extract_geometry_data(self):
        """提取几何数据"""
        self.logger.debug("提取几何数据")

        if not self._gltf_data or not self._gltf_data.meshes:
            self.logger.warning("没有网格数据")
            return

        self._geometries = []

        try:
            import numpy as np

            for mesh_index, mesh in enumerate(self._gltf_data.meshes):
                for primitive_index, primitive in enumerate(mesh.primitives):
                    geometry_data = {
                        "name": f"Mesh_{mesh_index}_Primitive_{primitive_index}",
                        "meshIndex": mesh_index,
                        "primitiveIndex": primitive_index,
                        "vertices": [],
                        "normals": [],
                        "uvs": [],
                        "indices": [],
                        "materialIndex": primitive.material if primitive.material is not None else 0
                    }

                    # 提取顶点位置
                    if primitive.attributes.POSITION is not None:
                        position_accessor = self._gltf_data.accessors[primitive.attributes.POSITION]
                        vertices = self._extract_accessor_data(position_accessor)
                        if vertices is not None:
                            geometry_data["vertices"] = vertices.tolist()

                    # 提取法线
                    if primitive.attributes.NORMAL is not None:
                        normal_accessor = self._gltf_data.accessors[primitive.attributes.NORMAL]
                        normals = self._extract_accessor_data(normal_accessor)
                        if normals is not None:
                            geometry_data["normals"] = normals.tolist()

                    # 提取UV坐标
                    if primitive.attributes.TEXCOORD_0 is not None:
                        uv_accessor = self._gltf_data.accessors[primitive.attributes.TEXCOORD_0]
                        uvs = self._extract_accessor_data(uv_accessor)
                        if uvs is not None:
                            geometry_data["uvs"] = uvs.tolist()

                    # 提取索引
                    if primitive.indices is not None:
                        index_accessor = self._gltf_data.accessors[primitive.indices]
                        indices = self._extract_accessor_data(index_accessor)
                        if indices is not None:
                            geometry_data["indices"] = indices.flatten().tolist()

                    # 只添加有效的几何数据
                    if geometry_data["vertices"]:
                        self._geometries.append(geometry_data)
                        self.logger.debug(f"提取几何体: {geometry_data['name']}, 顶点数: {len(geometry_data['vertices'])}")

        except ImportError:
            self.logger.error("numpy库未安装，无法解析几何数据")
        except Exception as e:
            self.logger.error(f"提取几何数据失败: {str(e)}", exc_info=True)

        self.logger.debug(f"提取到 {len(self._geometries)} 个几何体")
    
    def _extract_material_data(self):
        """提取材质数据"""
        self.logger.debug("提取材质数据")

        if not self._gltf_data or not self._gltf_data.materials:
            self.logger.warning("没有材质数据")
            # 创建默认材质
            self._materials = [{
                "name": "Default_Material",
                "type": "PrincipledMaterial",
                "baseColor": [0.8, 0.8, 0.8, 1.0],
                "metalness": 0.0,
                "roughness": 0.5,
                "diffuseTexture": "",
                "normalTexture": ""
            }]
            return

        self._materials = []

        try:
            for material_index, material in enumerate(self._gltf_data.materials):
                material_data = {
                    "name": material.name or f"Material_{material_index}",
                    "index": material_index,
                    "type": "PrincipledMaterial",  # 默认类型
                    "baseColor": [1.0, 1.0, 1.0, 1.0],
                    "metalness": 0.0,
                    "roughness": 1.0,
                    "diffuseTexture": "",
                    "normalTexture": "",
                    "emissiveTexture": "",
                    "emissiveFactor": [0.0, 0.0, 0.0]
                }

                # 提取PBR材质属性
                if material.pbrMetallicRoughness:
                    pbr = material.pbrMetallicRoughness

                    # 基础颜色
                    if pbr.baseColorFactor:
                        material_data["baseColor"] = list(pbr.baseColorFactor)

                    # 金属度和粗糙度
                    if pbr.metallicFactor is not None:
                        material_data["metalness"] = pbr.metallicFactor
                    if pbr.roughnessFactor is not None:
                        material_data["roughness"] = pbr.roughnessFactor

                    # 基础颜色纹理
                    if pbr.baseColorTexture:
                        texture_index = pbr.baseColorTexture.index
                        material_data["diffuseTexture"] = self._get_texture_path(texture_index)

                # 法线纹理
                if material.normalTexture:
                    texture_index = material.normalTexture.index
                    material_data["normalTexture"] = self._get_texture_path(texture_index)

                # 自发光
                if material.emissiveFactor:
                    material_data["emissiveFactor"] = list(material.emissiveFactor)
                if material.emissiveTexture:
                    texture_index = material.emissiveTexture.index
                    material_data["emissiveTexture"] = self._get_texture_path(texture_index)

                # 检查是否是MToon材质
                if material.extensions and 'VRMC_materials_mtoon' in material.extensions:
                    material_data.update(self._extract_mtoon_properties(material.extensions['VRMC_materials_mtoon']))
                    material_data["type"] = "MToon"
                elif material.extensions and 'VRM' in material.extensions:
                    # VRM 0.x MToon
                    material_data.update(self._extract_vrm0_mtoon_properties(material.extensions['VRM']))
                    material_data["type"] = "MToon"

                self._materials.append(material_data)
                self.logger.debug(f"提取材质: {material_data['name']} ({material_data['type']})")

        except Exception as e:
            self.logger.error(f"提取材质数据失败: {str(e)}", exc_info=True)
            # 创建默认材质作为回退
            self._materials = [{
                "name": "Fallback_Material",
                "type": "PrincipledMaterial",
                "baseColor": [0.8, 0.8, 0.8, 1.0],
                "metalness": 0.0,
                "roughness": 0.5
            }]

        self.logger.debug(f"提取到 {len(self._materials)} 个材质")
    
    def _extract_expression_data(self):
        """提取表情数据"""
        self.logger.debug("提取表情数据")

        self._expressions = []

        try:
            if not self._vrm_extension:
                self.logger.warning("没有VRM扩展数据，无法提取表情")
                return

            # VRM 1.0 表情数据提取
            if 'expressions' in self._vrm_extension:
                expressions_data = self._vrm_extension['expressions']

                # 预设表情
                if 'preset' in expressions_data:
                    for preset_name, preset_data in expressions_data['preset'].items():
                        expression = {
                            "name": preset_name,
                            "weight": 0.0,
                            "morphTargets": [],
                            "type": "preset"
                        }

                        # 提取morph targets
                        if 'morphTargetBinds' in preset_data:
                            for bind in preset_data['morphTargetBinds']:
                                if 'node' in bind and 'index' in bind:
                                    expression["morphTargets"].append({
                                        "node": bind['node'],
                                        "index": bind['index'],
                                        "weight": bind.get('weight', 1.0)
                                    })

                        self._expressions.append(expression)
                        self.logger.debug(f"提取预设表情: {preset_name}")

                # 自定义表情
                if 'custom' in expressions_data:
                    for custom_data in expressions_data['custom']:
                        expression = {
                            "name": custom_data.get('name', 'custom'),
                            "weight": 0.0,
                            "morphTargets": [],
                            "type": "custom"
                        }

                        # 提取morph targets
                        if 'morphTargetBinds' in custom_data:
                            for bind in custom_data['morphTargetBinds']:
                                if 'node' in bind and 'index' in bind:
                                    expression["morphTargets"].append({
                                        "node": bind['node'],
                                        "index": bind['index'],
                                        "weight": bind.get('weight', 1.0)
                                    })

                        self._expressions.append(expression)
                        self.logger.debug(f"提取自定义表情: {expression['name']}")

            # VRM 0.x 表情数据提取 (BlendShapeMaster)
            elif 'blendShapeMaster' in self._vrm_extension:
                blend_shape_groups = self._vrm_extension['blendShapeMaster'].get('blendShapeGroups', [])

                for group in blend_shape_groups:
                    expression = {
                        "name": group.get('name', 'unknown'),
                        "weight": 0.0,
                        "morphTargets": [],
                        "type": "blendShape",
                        "presetName": group.get('presetName', 'unknown')
                    }

                    # 提取binds
                    if 'binds' in group:
                        for bind in group['binds']:
                            if 'mesh' in bind and 'index' in bind:
                                expression["morphTargets"].append({
                                    "mesh": bind['mesh'],
                                    "index": bind['index'],
                                    "weight": bind.get('weight', 100.0) / 100.0  # VRM 0.x uses 0-100 range
                                })

                    self._expressions.append(expression)
                    self.logger.debug(f"提取BlendShape表情: {expression['name']} (preset: {expression['presetName']})")

            # 如果没有找到表情数据，创建默认表情
            if not self._expressions:
                self._expressions = [
                    {
                        "name": "neutral",
                        "weight": 1.0,
                        "morphTargets": [],
                        "type": "default"
                    }
                ]
                self.logger.info("未找到表情数据，创建默认neutral表情")

        except Exception as e:
            self.logger.error(f"提取表情数据失败: {str(e)}", exc_info=True)
            # 创建默认表情作为回退
            self._expressions = [
                {
                    "name": "neutral",
                    "weight": 1.0,
                    "morphTargets": [],
                    "type": "fallback"
                }
            ]

        self.logger.debug(f"提取到 {len(self._expressions)} 个表情")

    def _extract_accessor_data(self, accessor):
        """提取accessor数据"""
        try:
            import numpy as np

            if not accessor:
                return None

            # 获取buffer view
            buffer_view = self._gltf_data.bufferViews[accessor.bufferView]
            buffer_data = self._gltf_data.buffers[buffer_view.buffer]

            # 获取二进制数据 - 修复GLB文件buffer数据访问
            binary_data = None

            # 方法1: 直接从buffer对象获取数据
            if hasattr(buffer_data, 'data') and buffer_data.data:
                binary_data = buffer_data.data
                self.logger.debug(f"从buffer.data获取数据，长度: {len(binary_data)}")

            # 方法2: 从GLTF对象的二进制数据获取
            elif hasattr(self._gltf_data, 'binary_blob'):
                try:
                    # binary_blob可能是方法或属性
                    if callable(self._gltf_data.binary_blob):
                        binary_data = self._gltf_data.binary_blob()
                    else:
                        binary_data = self._gltf_data.binary_blob

                    if binary_data:
                        self.logger.debug(f"从binary_blob获取数据，长度: {len(binary_data)}")
                except Exception as e:
                    self.logger.debug(f"从binary_blob获取数据失败: {e}")
                    binary_data = None

            # 方法3: 尝试从文件重新读取二进制数据
            elif hasattr(self, '_model_path') and self._model_path:
                try:
                    with open(self._model_path, 'rb') as f:
                        # GLB文件格式：12字节头部 + JSON chunk + 可选的BIN chunk
                        f.seek(0)
                        magic = f.read(4)
                        if magic == b'glTF':
                            version = int.from_bytes(f.read(4), 'little')
                            total_length = int.from_bytes(f.read(4), 'little')

                            # 读取JSON chunk
                            json_chunk_length = int.from_bytes(f.read(4), 'little')
                            json_chunk_type = f.read(4)
                            f.seek(json_chunk_length, 1)  # 跳过JSON数据

                            # 读取BIN chunk
                            if f.tell() < total_length:
                                bin_chunk_length = int.from_bytes(f.read(4), 'little')
                                bin_chunk_type = f.read(4)
                                if bin_chunk_type == b'BIN\x00':
                                    binary_data = f.read(bin_chunk_length)
                                    self.logger.debug(f"从GLB文件直接读取二进制数据，长度: {len(binary_data)}")
                except Exception as e:
                    self.logger.debug(f"直接读取GLB二进制数据失败: {e}")

            # 方法4: 检查外部文件
            elif hasattr(buffer_data, 'uri') and buffer_data.uri:
                self.logger.warning("外部buffer文件暂不支持")
                return None

            if not binary_data:
                self.logger.warning("无法获取buffer数据")
                return None

            # 计算偏移和步长
            offset = (buffer_view.byteOffset or 0) + (accessor.byteOffset or 0)

            # 根据组件类型确定数据类型
            component_type_map = {
                5120: np.int8,    # BYTE
                5121: np.uint8,   # UNSIGNED_BYTE
                5122: np.int16,   # SHORT
                5123: np.uint16,  # UNSIGNED_SHORT
                5125: np.uint32,  # UNSIGNED_INT
                5126: np.float32, # FLOAT
            }

            if accessor.componentType not in component_type_map:
                self.logger.error(f"不支持的组件类型: {accessor.componentType}")
                return None

            dtype = component_type_map[accessor.componentType]

            # 根据类型确定组件数量
            type_component_count = {
                "SCALAR": 1,
                "VEC2": 2,
                "VEC3": 3,
                "VEC4": 4,
                "MAT2": 4,
                "MAT3": 9,
                "MAT4": 16
            }

            if accessor.type not in type_component_count:
                self.logger.error(f"不支持的数据类型: {accessor.type}")
                return None

            component_count = type_component_count[accessor.type]

            # 提取数据
            byte_length = accessor.count * component_count * dtype().itemsize
            data_bytes = binary_data[offset:offset + byte_length]

            # 转换为numpy数组
            data_array = np.frombuffer(data_bytes, dtype=dtype)

            # 重塑为正确的形状
            if component_count > 1:
                data_array = data_array.reshape(accessor.count, component_count)

            return data_array

        except Exception as e:
            self.logger.error(f"提取accessor数据失败: {str(e)}", exc_info=True)
            return None

    def _get_texture_path(self, texture_index: int) -> str:
        """获取纹理文件路径"""
        try:
            if not self._gltf_data.textures or texture_index >= len(self._gltf_data.textures):
                return ""

            texture = self._gltf_data.textures[texture_index]
            if not texture.source:
                return ""

            if texture.source >= len(self._gltf_data.images):
                return ""

            image = self._gltf_data.images[texture.source]
            if image.uri:
                return image.uri
            else:
                # 嵌入的图像数据，暂时返回占位符
                return f"embedded_texture_{texture_index}"

        except Exception as e:
            self.logger.error(f"获取纹理路径失败: {str(e)}")
            return ""

    def _extract_mtoon_properties(self, mtoon_extension: dict) -> dict:
        """提取MToon 1.0属性"""
        mtoon_props = {}

        try:
            # 基础属性
            if 'shadeColorFactor' in mtoon_extension:
                mtoon_props['shadeColor'] = mtoon_extension['shadeColorFactor']

            if 'shadingShiftFactor' in mtoon_extension:
                mtoon_props['shadingShift'] = mtoon_extension['shadingShiftFactor']

            if 'shadingToonyFactor' in mtoon_extension:
                mtoon_props['shadingToony'] = mtoon_extension['shadingToonyFactor']

            # 边缘光
            if 'parametricRimColorFactor' in mtoon_extension:
                mtoon_props['rimColor'] = mtoon_extension['parametricRimColorFactor']

            if 'parametricRimFresnelPowerFactor' in mtoon_extension:
                mtoon_props['rimFresnelPower'] = mtoon_extension['parametricRimFresnelPowerFactor']

            # 描边
            if 'outlineWidthFactor' in mtoon_extension:
                mtoon_props['outlineWidth'] = mtoon_extension['outlineWidthFactor']

            if 'outlineColorFactor' in mtoon_extension:
                mtoon_props['outlineColor'] = mtoon_extension['outlineColorFactor']

        except Exception as e:
            self.logger.error(f"提取MToon属性失败: {str(e)}")

        return mtoon_props

    def _extract_vrm0_mtoon_properties(self, vrm_extension: dict) -> dict:
        """提取VRM 0.x MToon属性"""
        mtoon_props = {}

        try:
            # VRM 0.x的MToon属性结构不同，这里做简化处理
            mtoon_props.update({
                'shadeColor': [0.8, 0.8, 0.8],
                'shadingShift': 0.0,
                'shadingToony': 0.9,
                'rimColor': [0.0, 0.0, 0.0],
                'outlineWidth': 0.0,
                'outlineColor': [0.0, 0.0, 0.0]
            })

        except Exception as e:
            self.logger.error(f"提取VRM 0.x MToon属性失败: {str(e)}")

        return mtoon_props

    def _extract_bone_data(self):
        """提取骨骼数据"""
        self.logger.debug("提取骨骼数据")

        self._bones = []

        try:
            if not self._gltf_data or not self._gltf_data.nodes:
                self.logger.warning("没有节点数据")
                return

            # 提取所有节点作为骨骼
            for node_index, node in enumerate(self._gltf_data.nodes):
                bone_data = {
                    "index": node_index,
                    "name": node.name or f"Bone_{node_index}",
                    "translation": list(node.translation) if node.translation else [0.0, 0.0, 0.0],
                    "rotation": list(node.rotation) if node.rotation else [0.0, 0.0, 0.0, 1.0],
                    "scale": list(node.scale) if node.scale else [1.0, 1.0, 1.0],
                    "children": list(node.children) if node.children else [],
                    "parent": -1  # 将在后续计算
                }

                # 如果有变换矩阵，优先使用
                if node.matrix:
                    bone_data["matrix"] = list(node.matrix)

                self._bones.append(bone_data)

            # 计算父子关系
            for bone in self._bones:
                for child_index in bone["children"]:
                    if child_index < len(self._bones):
                        self._bones[child_index]["parent"] = bone["index"]

            self.logger.debug(f"提取到 {len(self._bones)} 个骨骼")

        except Exception as e:
            self.logger.error(f"提取骨骼数据失败: {str(e)}", exc_info=True)

    def _extract_animation_data(self):
        """提取动画数据"""
        self.logger.debug("提取动画数据")

        self._animations = []

        try:
            if not self._gltf_data or not self._gltf_data.animations:
                self.logger.debug("没有动画数据")
                return

            for anim_index, animation in enumerate(self._gltf_data.animations):
                anim_data = {
                    "name": animation.name or f"Animation_{anim_index}",
                    "duration": 0.0,
                    "channels": []
                }

                # 提取动画通道
                for channel in animation.channels:
                    if channel.sampler < len(animation.samplers):
                        sampler = animation.samplers[channel.sampler]

                        channel_data = {
                            "target": {
                                "node": channel.target.node,
                                "path": channel.target.path  # translation, rotation, scale
                            },
                            "interpolation": sampler.interpolation or "LINEAR",
                            "input": [],  # 时间关键帧
                            "output": []  # 值关键帧
                        }

                        # 提取关键帧数据
                        try:
                            if sampler.input is not None:
                                input_accessor = self._gltf_data.accessors[sampler.input]
                                input_data = self._extract_accessor_data(input_accessor)
                                if input_data is not None:
                                    channel_data["input"] = input_data.tolist()
                                    # 更新动画总时长
                                    if len(input_data) > 0:
                                        anim_data["duration"] = max(anim_data["duration"], float(input_data[-1]))

                            if sampler.output is not None:
                                output_accessor = self._gltf_data.accessors[sampler.output]
                                output_data = self._extract_accessor_data(output_accessor)
                                if output_data is not None:
                                    channel_data["output"] = output_data.tolist()

                        except Exception as e:
                            self.logger.warning(f"提取动画关键帧失败: {str(e)}")

                        anim_data["channels"].append(channel_data)

                self._animations.append(anim_data)
                self.logger.debug(f"提取动画: {anim_data['name']}, 时长: {anim_data['duration']:.2f}s, 通道数: {len(anim_data['channels'])}")

            self.logger.debug(f"提取到 {len(self._animations)} 个动画")

        except Exception as e:
            self.logger.error(f"提取动画数据失败: {str(e)}", exc_info=True)

    def _extract_vrm_meta(self):
        """提取VRM元数据"""
        self.logger.debug("提取VRM元数据")

        self._vrm_meta = {}

        try:
            if not self._vrm_extension:
                self.logger.warning("没有VRM扩展数据")
                return

            # VRM 1.0 元数据
            if 'meta' in self._vrm_extension:
                meta_data = self._vrm_extension['meta']

                self._vrm_meta = {
                    "name": meta_data.get('name', ''),
                    "version": meta_data.get('version', ''),
                    "authors": meta_data.get('authors', []),
                    "copyrightInformation": meta_data.get('copyrightInformation', ''),
                    "contactInformation": meta_data.get('contactInformation', ''),
                    "references": meta_data.get('references', []),
                    "thirdPartyLicenses": meta_data.get('thirdPartyLicenses', ''),
                    "thumbnailImage": meta_data.get('thumbnailImage', None),
                    "licenseUrl": meta_data.get('licenseUrl', ''),
                    "avatarPermission": meta_data.get('avatarPermission', 'onlyAuthor'),
                    "allowExcessivelyViolentUsage": meta_data.get('allowExcessivelyViolentUsage', False),
                    "allowExcessivelySexualUsage": meta_data.get('allowExcessivelySexualUsage', False),
                    "commercialUsage": meta_data.get('commercialUsage', 'personalNonProfit'),
                    "allowPoliticalOrReligiousUsage": meta_data.get('allowPoliticalOrReligiousUsage', False),
                    "allowAntisocialOrHateUsage": meta_data.get('allowAntisocialOrHateUsage', False),
                    "creditNotation": meta_data.get('creditNotation', 'required'),
                    "allowRedistribution": meta_data.get('allowRedistribution', False),
                    "modification": meta_data.get('modification', 'prohibited')
                }

                self.logger.debug(f"提取VRM 1.0元数据: {self._vrm_meta.get('name', 'Unknown')}")

            # VRM 0.x 元数据
            elif 'meta' in self._vrm_extension:
                meta_data = self._vrm_extension['meta']

                self._vrm_meta = {
                    "title": meta_data.get('title', ''),
                    "version": meta_data.get('version', ''),
                    "author": meta_data.get('author', ''),
                    "contactInformation": meta_data.get('contactInformation', ''),
                    "reference": meta_data.get('reference', ''),
                    "texture": meta_data.get('texture', None),
                    "allowedUserName": meta_data.get('allowedUserName', 'OnlyAuthor'),
                    "violentUssageName": meta_data.get('violentUssageName', 'Disallow'),
                    "sexualUssageName": meta_data.get('sexualUssageName', 'Disallow'),
                    "commercialUssageName": meta_data.get('commercialUssageName', 'Disallow'),
                    "otherPermissionUrl": meta_data.get('otherPermissionUrl', ''),
                    "licenseName": meta_data.get('licenseName', 'Redistribution_Prohibited'),
                    "otherLicenseUrl": meta_data.get('otherLicenseUrl', '')
                }

                self.logger.debug(f"提取VRM 0.x元数据: {self._vrm_meta.get('title', 'Unknown')}")

            else:
                self.logger.warning("未找到VRM元数据")
                self._vrm_meta = {
                    "name": "Unknown VRM Model",
                    "version": "Unknown",
                    "authors": [],
                    "avatarPermission": "onlyAuthor"
                }

        except Exception as e:
            self.logger.error(f"提取VRM元数据失败: {str(e)}", exc_info=True)
            self._vrm_meta = {
                "name": "Error Loading Meta",
                "version": "Unknown",
                "authors": [],
                "avatarPermission": "onlyAuthor"
            }

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

try:
    import pygltflib
    
    # 测试文件路径
    vrm_file = Path(__file__).parent / "aipetv2/aipet/assets/vrm/models/Zome.vrm"
    
    if vrm_file.exists():
        print(f"文件存在: {vrm_file}")
        print(f"文件大小: {vrm_file.stat().st_size} bytes")
        
        # 尝试加载
        gltf_data = pygltflib.GLTF2.load_binary(str(vrm_file))
        print("GLTF加载成功")
        print(f"节点数: {len(gltf_data.nodes) if gltf_data.nodes else 0}")
        print(f"网格数: {len(gltf_data.meshes) if gltf_data.meshes else 0}")
        
    else:
        print(f"文件不存在: {vrm_file}")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

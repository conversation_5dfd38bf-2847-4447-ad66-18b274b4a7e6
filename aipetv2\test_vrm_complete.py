#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的VRM系统测试
测试VRMController和VRMDataProvider的集成功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, QTimer
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入VRM组件
from aipet.ui.components.vrm.vrm_controller import VRMController

class VRMCompleteTestRunner(QObject):
    """VRM完整测试运行器"""
    
    def __init__(self):
        super().__init__()
        self.app = None
        self.controller = None
        self.test_results = []
        
    def setup_application(self):
        """设置应用程序"""
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("VRM Complete Test")
        
        # 创建VRM控制器
        self.controller = VRMController()
        
        # 连接信号
        self.controller.modelChanged.connect(self.on_model_changed)
        self.controller.expressionChanged.connect(self.on_expression_changed)
        self.controller.errorOccurred.connect(self.on_error_occurred)
        self.controller.loadingProgress.connect(self.on_loading_progress)
        self.controller.metaDataLoaded.connect(self.on_meta_data_loaded)
        
        logger.info("✅ 应用程序设置完成")
        
    def test_all_vrm_models(self):
        """测试所有VRM模型"""
        # 测试文件路径
        test_files = [
            "aipetv2/aipet/assets/vrm/models/Zome.vrm",
            "aipetv2/aipet/assets/vrm/models/Alice.vrm", 
            "aipetv2/aipet/assets/vrm/models/aldina.vrm"
        ]
        
        logger.info("\n🔄 开始完整VRM系统测试...")
        
        for i, model_path in enumerate(test_files):
            full_path = project_root / model_path
            if full_path.exists():
                logger.info(f"\n{'='*60}")
                logger.info(f"📁 测试模型 {i+1}/3: {model_path}")
                logger.info(f"文件大小: {full_path.stat().st_size / (1024*1024):.1f} MB")
                logger.info(f"{'='*60}")
                
                # 测试模型加载
                success = self.test_model_loading(str(full_path))
                
                if success:
                    # 测试各种功能
                    self.test_model_info()
                    self.test_expression_control()
                    self.test_bone_data()
                    self.test_animation_data()
                    self.test_meta_data()
                    self.test_validation()
                else:
                    logger.error(f"❌ 模型加载失败，跳过功能测试")
                
                # 清除模型准备下一个测试
                self.controller.clearModel()
                logger.info("🧹 模型数据已清除")
                
            else:
                logger.warning(f"⚠️ 文件不存在: {full_path}")
        
        # 输出测试总结
        self.print_test_summary()
    
    def test_model_loading(self, file_path: str) -> bool:
        """测试模型加载"""
        logger.info("🔄 测试模型加载...")
        
        try:
            success = self.controller.loadModel(file_path)
            
            if success:
                logger.info("✅ 模型加载成功")
                self.test_results.append(("模型加载", "成功"))
                return True
            else:
                logger.error("❌ 模型加载失败")
                self.test_results.append(("模型加载", "失败"))
                return False
                
        except Exception as e:
            logger.error(f"❌ 模型加载异常: {str(e)}")
            self.test_results.append(("模型加载", f"异常: {str(e)}"))
            return False
    
    def test_model_info(self):
        """测试模型信息"""
        logger.info("🔄 测试模型信息...")
        
        try:
            logger.info(f"📊 模型名称: {self.controller.modelName}")
            logger.info(f"📊 几何体数量: {self.controller.geometryCount}")
            logger.info(f"📊 材质数量: {self.controller.materialCount}")
            logger.info(f"📊 表情数量: {self.controller.expressionCount}")
            logger.info(f"📊 骨骼数量: {self.controller.boneCount}")
            logger.info(f"📊 动画数量: {self.controller.animationCount}")
            
            # 获取详细信息
            model_info = self.controller.getModelInfo()
            logger.info(f"📊 模型详细信息: {len(model_info)} 个字段")
            
            self.test_results.append(("模型信息", "成功"))
            
        except Exception as e:
            logger.error(f"❌ 获取模型信息失败: {str(e)}")
            self.test_results.append(("模型信息", f"失败: {str(e)}"))
    
    def test_expression_control(self):
        """测试表情控制"""
        logger.info("🔄 测试表情控制...")
        
        try:
            expressions = self.controller.availableExpressions
            logger.info(f"📊 可用表情: {len(expressions)} 个")
            
            # 测试前3个表情
            for i, expr in enumerate(expressions[:3]):
                name = expr.get("name", f"expression_{i}")
                logger.info(f"   - {name}: 权重 {expr.get('weight', 0.0):.2f}")
                
                # 测试设置表情权重
                self.controller.setExpression(name, 0.5)
                weight = self.controller.getExpressionWeight(name)
                logger.info(f"     设置权重 0.5, 实际权重: {weight:.2f}")
            
            # 测试重置表情
            self.controller.resetExpressions()
            logger.info("✅ 表情重置完成")
            
            self.test_results.append(("表情控制", "成功"))
            
        except Exception as e:
            logger.error(f"❌ 表情控制测试失败: {str(e)}")
            self.test_results.append(("表情控制", f"失败: {str(e)}"))
    
    def test_bone_data(self):
        """测试骨骼数据"""
        logger.info("🔄 测试骨骼数据...")
        
        try:
            bones = self.controller.bones
            logger.info(f"📊 骨骼列表: {len(bones)} 个")
            
            # 显示前5个骨骼信息
            for bone in bones[:5]:
                name = bone.get("name", "Unknown")
                children_count = len(bone.get("children", []))
                logger.info(f"   - {name}: {children_count} 个子节点")
            
            # 测试骨骼变换获取
            if bones:
                first_bone = bones[0].get("name", "")
                if first_bone:
                    transform = self.controller.getBoneTransform(first_bone)
                    logger.info(f"   骨骼 {first_bone} 变换: {len(transform)} 个属性")
            
            self.test_results.append(("骨骼数据", "成功"))
            
        except Exception as e:
            logger.error(f"❌ 骨骼数据测试失败: {str(e)}")
            self.test_results.append(("骨骼数据", f"失败: {str(e)}"))
    
    def test_animation_data(self):
        """测试动画数据"""
        logger.info("🔄 测试动画数据...")
        
        try:
            animations = self.controller.animations
            available_anims = self.controller.getAvailableAnimations()
            
            logger.info(f"📊 动画数据: {len(animations)} 个")
            logger.info(f"📊 可用动画: {len(available_anims)} 个")
            
            for anim_name in available_anims[:3]:
                logger.info(f"   - {anim_name}")
            
            self.test_results.append(("动画数据", "成功"))
            
        except Exception as e:
            logger.error(f"❌ 动画数据测试失败: {str(e)}")
            self.test_results.append(("动画数据", f"失败: {str(e)}"))
    
    def test_meta_data(self):
        """测试元数据"""
        logger.info("🔄 测试VRM元数据...")
        
        try:
            meta = self.controller.vrmMeta
            logger.info(f"📊 VRM元数据: {len(meta)} 个字段")
            
            if meta:
                name = meta.get('name', meta.get('title', 'Unknown'))
                version = meta.get('version', 'Unknown')
                authors = meta.get('authors', meta.get('author', 'Unknown'))
                
                logger.info(f"   - 名称: {name}")
                logger.info(f"   - 版本: {version}")
                logger.info(f"   - 作者: {authors}")
            
            self.test_results.append(("VRM元数据", "成功"))
            
        except Exception as e:
            logger.error(f"❌ VRM元数据测试失败: {str(e)}")
            self.test_results.append(("VRM元数据", f"失败: {str(e)}"))
    
    def test_validation(self):
        """测试模型验证"""
        logger.info("🔄 测试模型验证...")
        
        try:
            is_valid = self.controller.validateModel()
            logger.info(f"📊 模型验证结果: {'通过' if is_valid else '失败'}")
            
            # 获取调试信息
            debug_info = self.controller.getDebugInfo()
            logger.info(f"📊 调试信息: {len(debug_info)} 个字段")
            
            self.test_results.append(("模型验证", "成功" if is_valid else "失败"))
            
        except Exception as e:
            logger.error(f"❌ 模型验证测试失败: {str(e)}")
            self.test_results.append(("模型验证", f"失败: {str(e)}"))
    
    def print_test_summary(self):
        """打印测试总结"""
        logger.info("\n" + "="*60)
        logger.info("🎉 测试总结")
        logger.info("="*60)
        
        success_count = 0
        total_count = len(self.test_results)
        
        for test_name, result in self.test_results:
            status = "✅" if "成功" in result else "❌"
            logger.info(f"{status} {test_name}: {result}")
            if "成功" in result:
                success_count += 1
        
        logger.info(f"\n📊 总计: {success_count}/{total_count} 项测试通过")
        logger.info(f"📊 成功率: {success_count/total_count*100:.1f}%")
    
    # 信号回调方法
    def on_model_changed(self):
        logger.debug("📡 模型数据已更新")
    
    def on_expression_changed(self, name: str, weight: float):
        logger.debug(f"📡 表情变化: {name} = {weight:.2f}")
    
    def on_loading_progress(self, progress: int):
        logger.info(f"⏳ 加载进度: {progress}%")
    
    def on_error_occurred(self, error_msg: str):
        logger.error(f"❌ 错误: {error_msg}")
    
    def on_meta_data_loaded(self, meta_data):
        logger.debug(f"📡 VRM元数据已加载: {len(meta_data)} 个字段")
    
    def run_test(self):
        """运行测试"""
        try:
            self.setup_application()
            
            # 使用定时器延迟执行测试
            QTimer.singleShot(100, self.test_all_vrm_models)
            QTimer.singleShot(10000, self.app.quit)  # 10秒后退出
            
            # 启动事件循环
            return self.app.exec()
            
        except Exception as e:
            logger.error(f"测试运行失败: {str(e)}", exc_info=True)
            return 1

def main():
    """主函数"""
    print("🚀 启动VRM完整系统测试")
    
    try:
        # 检查依赖
        try:
            import pygltflib
            logger.info("✅ pygltflib 库已安装")
        except ImportError:
            logger.error("❌ pygltflib 库未安装，请运行: pip install pygltflib")
            return 1
        
        # 运行测试
        test_runner = VRMCompleteTestRunner()
        return test_runner.run_test()
        
    except KeyboardInterrupt:
        logger.info("🛑 测试被用户中断")
        return 0
    except Exception as e:
        logger.error(f"测试失败: {str(e)}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(main())

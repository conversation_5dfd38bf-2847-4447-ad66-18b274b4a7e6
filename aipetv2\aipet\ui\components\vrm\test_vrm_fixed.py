#!/usr/bin/env python3
"""
修复后的VRM模型渲染测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtQml import QQmlApplicationEngine, qmlRegisterType
from PySide6.QtCore import QUrl

# 导入VRM组件
from aipet.ui.components.vrm.vrm_controller import VRMController

def main():
    """主函数"""
    print("🚀 修复后的VRM模型渲染测试")
    print("=" * 50)
    
    # 设置Qt Quick Controls样式为Material以支持自定义
    os.environ["QT_QUICK_CONTROLS_STYLE"] = "Material"
    
    # 设置Qt Quick 3D环境变量
    os.environ["QT_QUICK_3D_CORE_PROFILE"] = "1"
    
    app = QApplication(sys.argv)
    app.setApplicationName("Fixed VRM Test")
    
    # 创建QML引擎
    engine = QQmlApplicationEngine()
    
    # 注册VRM类型
    qmlRegisterType(VRMController, "VRM", 1, 0, "VRMController")
    
    # 创建VRM控制器
    vrm_controller = VRMController()
    
    # 注册到QML上下文
    engine.rootContext().setContextProperty("vrmController", vrm_controller)
    
    # 加载测试模型
    test_model = project_root / "aipet/assets/vrm/models/aldina.vrm"
    if test_model.exists():
        print(f"📁 加载测试模型: {test_model}")
        success = vrm_controller.loadModel(str(test_model))
        if success:
            print(f"✅ 模型加载成功: {vrm_controller.modelName}")
            print(f"📊 几何体数量: {len(vrm_controller.geometries)}")
            print(f"🎨 材质数量: {len(vrm_controller.materials)}")
        else:
            print("❌ 模型加载失败")
            return 1
    else:
        print(f"❌ 测试模型不存在: {test_model}")
        return 1
    
    # 加载QML界面
    qml_file = Path(__file__).parent / "qml" / "VRMTestScene.qml"
    if not qml_file.exists():
        print(f"❌ QML文件不存在: {qml_file}")
        return 1
    
    print(f"🔄 加载QML界面: {qml_file}")
    engine.load(QUrl.fromLocalFile(str(qml_file)))
    
    if not engine.rootObjects():
        print("❌ QML界面加载失败")
        return 1
    
    print("✅ 程序启动成功!")
    print("\n💡 修复内容:")
    print("  - 使用Material样式避免自定义控件警告")
    print("  - 用几何体代替直接加载VRM文件")
    print("  - 显示从Python解析的VRM数据")
    print("  - 根据材质信息设置颜色和属性")
    
    # 运行应用程序
    result = app.exec()
    
    # 清理
    vrm_controller.clear()
    print("🧹 资源清理完成")
    
    return result

if __name__ == "__main__":
    sys.exit(main())